import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    // Parse the request body
    const data = await request.json();
    const { event_id, child_age_months } = data;
    
    if (!event_id || isNaN(Number(event_id)) || Number(event_id) <= 0) {
      return NextResponse.json(
        { error: "Invalid event ID. ID must be a positive number." },
        { status: 400 }
      );
    }
    
    console.log(`Server API route: Fetching event with games for ID: ${event_id}, child age: ${child_age_months} months`);

    // Forward the request to the external API
    const apiUrl = "https://ai.alviongs.com/webhook/v1/nibog/event-registration/get";
    console.log("Server API route: Calling API URL:", apiUrl);

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ id: Number(event_id) }),
      cache: "no-store",
    });

    console.log(`Server API route: Get event response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Server API route: Error response:", errorText);
      return NextResponse.json(
        { error: `Failed to fetch event: ${response.status}` },
        { status: response.status }
      );
    }

    const eventData = await response.json();
    console.log("Server API route: Retrieved event data:", eventData);

    // The API returns an array with a single event object
    const event = Array.isArray(eventData) ? eventData[0] : eventData;
    
    if (!event) {
      return NextResponse.json(
        { error: `Event with ID ${event_id} not found` },
        { status: 404 }
      );
    }

    // Extract games from the event
    let eventGames = event.games || [];

    // Log raw game data for debugging
    console.log("Raw game data:", JSON.stringify(eventGames.slice(0, 2), null, 2));

    // Filter games by child age if provided
    if (child_age_months && typeof child_age_months === 'number') {
      console.log(`Filtering games for child age: ${child_age_months} months`);

      eventGames = eventGames.filter((game: any) => {
        // Skip games without age restrictions
        if (!game.min_age || !game.max_age) {
          console.log(`Game ${game.game_title} has no age restrictions, including it`);
          return true;
        }

        // Based on API documentation, min_age and max_age are already in months
        const minAgeInMonths = typeof game.min_age === 'string' ?
          parseInt(game.min_age, 10) :
          game.min_age;
        const maxAgeInMonths = typeof game.max_age === 'string' ?
          parseInt(game.max_age, 10) :
          game.max_age;

        const isEligible = child_age_months >= minAgeInMonths && child_age_months <= maxAgeInMonths;

        console.log(`Game ${game.game_title}: min=${minAgeInMonths}mo, max=${maxAgeInMonths}mo, child=${child_age_months}mo, eligible=${isEligible}`);

        return isEligible;
      });

      console.log(`Filtered to ${eventGames.length} age-appropriate games`);
    } else {
      console.log(`No age filtering applied - showing all ${eventGames.length} games`);
    }

    // Return the event data with filtered games
    const responseData = {
      event_id: event.event_id,
      event_title: event.event_title,
      event_description: event.event_description,
      event_date: event.event_date,
      event_status: event.event_status,
      city_name: event.city_name,
      venue_name: event.venue_name,
      event_games: eventGames
    };

    console.log(`Server API route: Returning ${eventGames.length} games for event`);
    return NextResponse.json(responseData);

  } catch (error: any) {
    console.error("Server API route: Error fetching event:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
